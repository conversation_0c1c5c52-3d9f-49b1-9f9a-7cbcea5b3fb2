<script setup lang="ts">
import { closeToast, showFailToast, showLoadingToast, showSuccessToast } from 'vant'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'

// 类型定义
interface ImageItem {
  url: string
  description?: string
  time?: string
  iconImg?: string
  isQualified?: boolean
}

// 状态管理
const images = ref<ImageItem[]>([])
const currentIndex = ref(0)
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')
const isNavHidden = ref(false)
const imageLoadingStates = ref<Record<number, boolean>>({})
const imageErrorStates = ref<Record<number, boolean>>({})

const router = useRouter()

// 当前显示的图片信息
const currentImage = computed(() => {
  if (images.value.length === 0) {
    return { url: '', description: '', time: '', iconImg: '', isQualified: true }
  }
  return images.value[currentIndex.value]
})

// 初始化数据
onMounted(async () => {
  try {
    isLoading.value = true
    hasError.value = false

    // 从sessionStorage中获取图片数据
    const imageData = sessionStorage.getItem('previewImageData')
    const initialIndex = sessionStorage.getItem('previewCurrentIndex')

    if (imageData) {
      const parsedData = JSON.parse(imageData)
      const parsedIndex = initialIndex ? Number.parseInt(initialIndex, 10) : 0

      if (Array.isArray(parsedData) && parsedData.length > 0) {
        images.value = parsedData
        currentIndex.value = Math.max(0, Math.min(parsedIndex, parsedData.length - 1))

        // 初始化图片加载状态
        parsedData.forEach((_, index) => {
          imageLoadingStates.value[index] = true
          imageErrorStates.value[index] = false
        })

        isLoading.value = false
      }
      else {
        throw new Error('图片数据为空或格式错误')
      }
    }
    else {
      throw new Error('图片数据为空或格式错误')
    }
  }
  catch (error) {
    console.error('初始化预览页面失败:', error)
    hasError.value = true
    isLoading.value = false
    errorMessage.value = error instanceof Error ? error.message : '获取图片数据失败'
  }
})

// 清理缓存
onUnmounted(() => {
  try {
    sessionStorage.removeItem('previewImageData')
    sessionStorage.removeItem('previewCurrentIndex')
  }
  catch (error) {
    console.warn('清理缓存失败:', error)
  }
})

// 处理轮播图变化
function handleSwiperChange(index: number) {
  currentIndex.value = index
}

// 返回上一页
function handleBack() {
  if (window.history.length > 1) {
    router.back()
  }
  else {
    router.replace('/home')
  }
}

// 重试加载
function handleRetry() {
  // 重新初始化
  isLoading.value = true
  hasError.value = false

  // 重新获取数据
  const imageData = sessionStorage.getItem('previewImageData')
  const initialIndex = sessionStorage.getItem('previewCurrentIndex')

  if (imageData) {
    try {
      const parsedData = JSON.parse(imageData)
      const parsedIndex = initialIndex ? Number.parseInt(initialIndex, 10) : 0

      if (Array.isArray(parsedData) && parsedData.length > 0) {
        images.value = parsedData
        currentIndex.value = Math.max(0, Math.min(parsedIndex, parsedData.length - 1))

        // 初始化图片加载状态
        parsedData.forEach((_, index) => {
          imageLoadingStates.value[index] = true
          imageErrorStates.value[index] = false
        })

        isLoading.value = false
      }
      else {
        throw new Error('图片数据为空或格式错误')
      }
    }
    catch (error) {
      console.error('重试加载失败:', error)
      hasError.value = true
      isLoading.value = false
      errorMessage.value = error instanceof Error ? error.message : '获取图片数据失败'
    }
  }
}

// 切换导航栏显示状态
function toggleNavBar() {
  isNavHidden.value = !isNavHidden.value
}

// 处理图片加载完成
function handleImageLoad(index: number) {
  imageLoadingStates.value[index] = false
  imageErrorStates.value[index] = false
}

// 处理图片加载失败
function handleImageError(index: number) {
  imageLoadingStates.value[index] = false
  imageErrorStates.value[index] = true
}

// 重试加载单张图片
function retryLoadImage(index: number) {
  imageLoadingStates.value[index] = true
  imageErrorStates.value[index] = false

  // 触发图片重新加载（通过修改src实现）
  const image = images.value[index]
  if (image) {
    const originalUrl = image.url
    image.url = ''
    setTimeout(() => {
      image.url = originalUrl
    }, 100)
  }
}

// 显示图片操作菜单
function showImageActions(url: string) {
  // 创建一个简单的操作菜单
  const actions = ['保存图片', '分享图片', '取消']
  const actionSheet = document.createElement('div')
  actionSheet.className = 'action-sheet-overlay'
  actionSheet.innerHTML = `
    <div class="action-sheet">
      ${actions.map((action, index) =>
        `<div class="action-item ${action === '取消' ? 'cancel' : ''}" data-index="${index}">${action}</div>`,
      ).join('')}
    </div>
  `

  actionSheet.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    if (target.classList.contains('action-item')) {
      const index = Number.parseInt(target.dataset.index || '0', 10)
      if (index === 0)
        saveImage(url)
      else if (index === 1)
        shareImage(url)
      document.body.removeChild(actionSheet)
    }
    else if (target.classList.contains('action-sheet-overlay')) {
      document.body.removeChild(actionSheet)
    }
  })

  document.body.appendChild(actionSheet)
}

// 下载当前图片
function handleDownload() {
  if (currentImage.value.url) {
    saveImage(currentImage.value.url)
  }
}

// 保存图片到本地
async function saveImage(url: string) {
  try {
    showLoadingToast({
      message: '保存中...',
      forbidClick: true,
    })

    // 创建一个临时的a标签来下载图片
    const link = document.createElement('a')
    link.href = url
    link.download = `image_${Date.now()}.jpg`
    link.target = '_blank'

    // 对于跨域图片，需要先转换为blob
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const objectUrl = URL.createObjectURL(blob)
      link.href = objectUrl

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理对象URL
      URL.revokeObjectURL(objectUrl)
    }
    catch {
      // 如果fetch失败，直接使用原URL
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    closeToast()
    showSuccessToast('保存成功')
  }
  catch (error) {
    closeToast()
    console.error('保存图片失败:', error)
    showFailToast('保存失败')
  }
}

// 分享图片
function shareImage(url: string) {
  if (navigator.share) {
    navigator.share({
      title: '分享图片',
      url,
    }).catch((error) => {
      console.error('分享失败:', error)
      showFailToast('分享失败')
    })
  }
  else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(url).then(() => {
      showSuccessToast('图片链接已复制到剪贴板')
    }).catch(() => {
      showFailToast('分享功能不支持')
    })
  }
}
</script>

<template>
  <div class="image-preview-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <VanLoading type="spinner" />
        <span class="loading-text">
          加载中...
        </span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-overlay">
      <div class="error-content">
        <VanIcon name="info-o" size="48" color="#ff4444" />
        <span class="error-text">
          {{ errorMessage }}
        </span>
        <div class="error-actions">
          <VanButton size="small" @click="handleRetry">
            重试
          </VanButton>
          <VanButton size="small" @click="handleBack">
            返回
          </VanButton>
        </div>
      </div>
    </div>

    <!-- 正常预览状态 -->
    <template v-else>
      <!-- 顶部导航栏 -->
      <div class="nav-bar" :class="{ 'nav-bar-hidden': isNavHidden }">
        <div class="back-button" @click="handleBack">
          <VanIcon name="cross" size="24" color="#fff" />
        </div>
        <div class="counter">
          <span>{{ currentIndex + 1 }}/{{ images.length }}</span>
        </div>
        <div class="nav-actions">
          <div class="action-button" @click="handleDownload">
            <VanIcon name="down" size="20" color="#fff" />
          </div>
        </div>
      </div>

      <!-- 图片轮播区域 -->
      <VanSwipe
        class="swiper-container"
        :initial-swipe="currentIndex"
        :show-indicators="false"
        :autoplay="0"
        :loop="images.length > 1"
        @change="handleSwiperChange"
        @click="toggleNavBar"
      >
        <VanSwipeItem v-for="(item, index) in images" :key="`${index}-${item.url}`" class="swiper-item">
          <div class="image-container">
            <img
              :src="item.url"
              class="preview-image"
              :class="{ 'image-loading': imageLoadingStates[index] }"
              @load="handleImageLoad(index)"
              @error="handleImageError(index)"
              @contextmenu.prevent="showImageActions(item.url)"
              @click.stop="toggleNavBar"
            >

            <!-- 图片加载状态 -->
            <div v-if="imageLoadingStates[index]" class="image-loading-overlay">
              <VanLoading type="spinner" />
            </div>

            <!-- 图片加载失败状态 -->
            <div v-if="imageErrorStates[index]" class="image-error-overlay" @click="retryLoadImage(index)">
              <VanIcon name="replay" size="32" color="#fff" />
              <span class="image-error-text">
                点击重试
              </span>
            </div>

            <!-- 状态图标 -->
            <img
              v-if="item.iconImg && !imageErrorStates[index]"
              :src="item.iconImg"
              class="status-icon"
            >
          </div>
        </VanSwipeItem>
      </VanSwipe>

      <!-- 底部信息栏 -->
      <div class="info-bar" :class="{ 'info-bar-hidden': isNavHidden }">
        <div v-if="currentImage.description" class="description">
          <span>{{ currentImage.description }}</span>
        </div>
        <div v-if="currentImage.time" class="time">
          <span>{{ currentImage.time }}</span>
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
.image-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000;
  display: flex;
  flex-direction: column;
  z-index: 999;
}

.nav-bar {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 10;
}

.back-button {
  font-size: 18px;
  padding: 5px;
  cursor: pointer;
}

.counter {
  font-size: 14px;
}

.swiper-container {
  flex: 1;
  width: 100%;
  height: 100%;
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

// 加载状态样式
.loading-overlay,
.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  z-index: 1000;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #fff;
}

.loading-text,
.error-text {
  font-size: 16px;
  color: #fff;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

// 导航栏隐藏动画
.nav-bar {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;

  &.nav-bar-hidden {
    opacity: 0;
    transform: translateY(-100%);
    pointer-events: none;
  }
}

.back-button {
  &:active {
    opacity: 0.7;
  }
}

.counter {
  font-weight: 500;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-button {
  padding: 5px;
  cursor: pointer;

  &:active {
    opacity: 0.7;
  }
}

// 图片容器优化
.image-container {
  position: relative;
}

.preview-image {
  transition: opacity 0.3s ease;

  &.image-loading {
    opacity: 0.5;
  }
}

// 图片加载状态
.image-loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

// 图片错误状态
.image-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  z-index: 5;
  cursor: pointer;
}

.image-error-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8px;
}

// 状态图标
.status-icon {
  position: absolute;
  top: 33%;
  right: 16px;
  width: 64px;
  height: 64px;
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.5));
  z-index: 10;
}

// 底部信息栏
.info-bar {
  padding: 12px 15px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;

  &.info-bar-hidden {
    opacity: 0;
    transform: translateY(100%);
    pointer-events: none;
  }
}

.description {
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

// 操作菜单样式
:global(.action-sheet-overlay) {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

:global(.action-sheet) {
  background-color: #fff;
  border-radius: 12px 12px 0 0;
  width: 100%;
  max-width: 500px;
  animation: slideUp 0.3s ease;
}

:global(.action-item) {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  font-size: 16px;

  &:hover {
    background-color: #f5f5f5;
  }

  &.cancel {
    color: #999;
    border-bottom: none;
  }

  &:last-child {
    border-bottom: none;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .nav-bar {
    height: 40px;
    padding: 0 12px;
  }

  .counter {
    font-size: 13px;
  }

  .status-icon {
    width: 48px;
    height: 48px;
    right: 12px;
  }

  .info-bar {
    padding: 10px 12px;
  }

  .description {
    font-size: 13px;
  }

  .time {
    font-size: 11px;
  }
}
</style>
