<script lang="ts" setup>
import type { ItemListElement, V1LocationHomeProjectSubscribePostResponseResult } from '@/api/api.model'
import { showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { V1LocationHomeProjectSubscribePost, V1LocationHomeProjectSubscribeSubmitPost } from '@/api/api.req'
import { currentUserInfo } from '@/store/sysUser'

const route = useRoute()
const router = useRouter()

// 工位信息数据
const workstationInfo = ref<V1LocationHomeProjectSubscribePostResponseResult>({})
const loading = ref(true)
const videoPlayer = ref<HTMLVideoElement>()
const subscribeProjects = ref<ItemListElement[]>([])
const showSubscribeDialog = ref(false)
const subscribeLoading = ref(false)

// 计算已订阅和可订阅的项目数量
const subscribedCount = computed(() =>
  subscribeProjects.value.filter(project => project.subscribed).length,
)

const availableCount = computed(() =>
  subscribeProjects.value.filter(project => !project.originalSubscribed).length,
)

// 初始化工位信息
async function initWorkstationInfo() {
  try {
    loading.value = true

    // 从路由查询参数中获取基础工位信息
    const data = route.query.data as string
    let basicInfo: any = {}

    if (data) {
      basicInfo = JSON.parse(decodeURIComponent(data))
    }

    // 获取当前用户信息
    const userId = currentUserInfo.value.id
    if (!userId) {
      showToast('用户信息获取失败')
      return
    }

    // 调用后端接口获取完整的工位信息、视频地址和订阅项目列表
    const locationId = basicInfo.locationId || basicInfo.workstationId
    if (locationId) {
      const response = await V1LocationHomeProjectSubscribePost({
        locationId,
        userId,
        projectCode: basicInfo.countAlgorithm,
      })
      if (response) {
        workstationInfo.value = response
        // 保存原始订阅状态，用于区分新订阅的项目
        subscribeProjects.value = (response.itemList || []).map(project => reactive({
          ...project,
          originalSubscribed: project.subscribed,
        }))
      }
    }
    else {
      // 如果没有locationId，使用基础信息
      workstationInfo.value = {
        locationName: basicInfo.workstationName,
        projectCodeName: basicInfo.projectName,
        itemList: [],
      }
    }
  }
  catch (error) {
    console.error('获取工位信息失败:', error)
    showToast('获取工位信息失败')
  }
  finally {
    loading.value = false
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initWorkstationInfo()
})

// 获取视频URL（配置的视频或默认视频）
const videoUrl = computed(() => {
  return workstationInfo.value.fileVo?.url || '/example.mp4'
})

// 显示订阅项目弹窗
function showSubscribeProjectDialog() {
  if (availableCount.value === 0) {
    showToast('暂无可订阅项目')
    return
  }
  showSubscribeDialog.value = true
}

// 关闭订阅弹窗
function closeSubscribeDialog() {
  showSubscribeDialog.value = false
}

// 处理开关状态变化
function handleSwitchChange(project: ItemListElement, value: boolean) {
  // 如果已经订阅了，不允许取消订阅
  if (project.originalSubscribed && !value) {
    showToast('该项目已订阅，无法取消')
    // 恢复为订阅状态
    project.subscribed = true
    return
  }

  // 对于未订阅的项目，允许切换状态
  project.subscribed = value
}

// 处理确认订阅
async function handleConfirmSubscription() {
  try {
    subscribeLoading.value = true

    const userId = currentUserInfo.value.id
    if (!userId) {
      showToast('用户信息获取失败')
      return
    }

    // 获取新订阅的项目ID列表（只提交新订阅的项目）
    const newSubscribedProjectIds = subscribeProjects.value
      .filter(project => project.subscribed && !project.originalSubscribed)
      .map(project => project.id!)
      .filter(Boolean)

    if (newSubscribedProjectIds.length === 0) {
      showToast('请选择要订阅的项目')
      return
    }

    // 调用订阅接口
    const response = await V1LocationHomeProjectSubscribeSubmitPost({
      userId,
      projectIdList: newSubscribedProjectIds,
    })

    if (response) {
      // 更新订阅状态
      subscribeProjects.value.forEach((project) => {
        if (project.subscribed && !project.originalSubscribed) {
          project.originalSubscribed = true
        }
      })
      showToast('订阅成功')
      closeSubscribeDialog()
    }
    else {
      showToast('订阅失败')
    }
  }
  catch (error) {
    console.error('订阅失败:', error)
    showToast('订阅失败')
  }
  finally {
    subscribeLoading.value = false
  }
}

// 返回上一页
function goBack() {
  router.go(-1)
}
</script>

<template>
  <div class="workstation-info-page">
    <VanNavBar
      title="工位信息"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <div v-if="loading" class="loading-container">
      <VanLoading type="spinner" />
      <div class="mt-2">
        加载中...
      </div>
    </div>

    <div v-else class="content p-4 pb-20">
      <div class="info-card rounded-lg bg-white p-4 shadow-sm">
        <div class="header mb-4">
          <div class="title text-lg text-gray-8 font-bold">
            {{ workstationInfo.locationName || '未知工位' }}
          </div>
          <div v-if="workstationInfo.projectCodeName" class="subtitle mt-1 text-sm text-gray-5">
            当前项目：{{ workstationInfo.projectCodeName }}
          </div>
        </div>

        <div class="info-list">
          <div v-if="currentUserInfo.name" class="info-item">
            <div class="label">
              当前用户
            </div>
            <div class="value">
              {{ currentUserInfo.name }}
            </div>
          </div>

          <div v-if="workstationInfo.projectCodeName" class="info-item">
            <div class="label">
              当前项目
            </div>
            <div class="value">
              {{ workstationInfo.projectCodeName }}
            </div>
          </div>

          <div v-if="subscribeProjects.length" class="info-item">
            <div class="label">
              项目订阅
            </div>
            <div class="value">
              <span class="text-green-600">{{ subscribedCount }}</span> 已订阅
              <span v-if="availableCount > 0" class="ml-2 text-blue-600">{{ availableCount }} 可订阅</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习视频区域 -->
      <div class="video-section mt-4">
        <div class="section-title mb-3 text-lg text-gray-8 font-bold">
          学习视频
        </div>
        <div class="video-container rounded-lg bg-white shadow-sm">
          <video
            ref="videoPlayer"
            width="100%"
            height="200"
            controls
            preload="auto"
            class="rounded-lg"
          >
            <source :src="videoUrl" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>
    </div>

    <!-- 吸底按钮 -->
    <div class="fixed-bottom-button">
      <VanButton
        :disabled="availableCount <= 0"
        type="primary"
        size="large"
        block
        @click="showSubscribeProjectDialog"
      >
        管理项目订阅
      </VanButton>
    </div>

    <!-- 项目订阅弹窗 -->
    <van-dialog
      v-model:show="showSubscribeDialog"
      title="项目订阅管理"
      :close-on-click-overlay="false"
      width="90%"
      show-cancel-button
      confirm-button-text="确认订阅"
      :confirm-button-loading="subscribeLoading"
      @close="closeSubscribeDialog"
      @confirm="handleConfirmSubscription"
    >
      <div class="subscribe-dialog-content p-4">
        <div v-if="subscribeProjects.length" class="project-list">
          <div
            v-for="project in subscribeProjects"
            :key="project.id"
            class="project-item flex items-center justify-between border-b border-gray-200 py-3 last:border-b-0" :class="[
              { subscribed: project.originalSubscribed },
            ]"
          >
            <div class="project-info flex-1">
              <div class="project-name font-medium">
                {{ project.projectName }}
              </div>
              <div v-if="project.type !== undefined" class="project-type mt-1 text-sm text-gray-500">
                {{ project.type === 0 ? '管理员订阅' : '自主订阅' }}
              </div>
              <div v-if="project.originalSubscribed" class="project-status mt-1 text-xs text-green-600">
                已订阅
              </div>
            </div>
            <VanSwitch
              v-model="project.subscribed"
              size="20px"
              :disabled="project.originalSubscribed"
              @update:model-value="(value) => handleSwitchChange(project, value)"
            />
          </div>
        </div>
        <div v-else class="py-8 text-center text-gray-500">
          暂无可订阅项目
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style lang="less" scoped>
.workstation-info-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.info-card {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .label {
      color: #666;
      font-size: 14px;
    }

    .value {
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.video-section {
  .section-header {
    background: linear-gradient(135deg, #4f8ef7 0%, #3b82f6 100%);
  }

  .video-content {
    border-top: none;
  }
}

.subscribe-section {
  .subscribe-btn {
    height: 48px;
    font-size: 16px;
    font-weight: bold;

    &.van-button--default {
      background-color: #f0f0f0;
      color: #666;
      border-color: #f0f0f0;
    }
  }
}

.video-dialog-content {
  .video-player {
    video {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.subscribe-dialog-content {
  .project-item {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    .project-name {
      color: #333;
      font-size: 16px;
    }

    .project-type {
      color: #666;
      font-size: 12px;
    }

    .project-status {
      color: #52c41a;
      font-size: 12px;
      font-weight: 500;
    }

    // 已订阅项目的样式
    &.subscribed {
      opacity: 0.7;

      .project-name {
        color: #999;
      }
    }
  }

  .dialog-actions {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
    margin-top: 16px;
  }
}

// 吸底按钮样式
.fixed-bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}
</style>
