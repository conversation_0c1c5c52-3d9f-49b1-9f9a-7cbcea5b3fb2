<script lang="ts" setup>
import type { V1ManageTrainBookingsPostRequestBody } from '@/api/api.model'
import { V1ManageSysUnitTerminalList, V1ManageTrainBookingsPost } from '@/api/api.req'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useLoading } from '@/hooks/useLoading'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { currentUserInfo } from '@/store/sysUser'
import dayjs from 'dayjs'
import { showToast } from 'vant'
import type { FormExpose } from 'vant/es/form/types'

const router = useRouter()
const query = useRouteQuery<{
  station?: string
  date?: string
}>()

const formState = reactive({
  station: query.station ?? '',
  date: query.date ? dayjs(query.date, 'YYYY-MM-DD').format(`MM-DD`).split('-') : [],
  startTime: [],
  endTime: [],
})

const formRef = ref<FormExpose>()

const datePickerConfig = {
  minDate: dayjs().startOf('d').toDate(),
  maxDate: dayjs().startOf('d').add(6, 'd').toDate(),
}

const stationsApi = useAsyncData(async () => {
  const resp = await V1ManageSysUnitTerminalList()

  return resp.map((item) => ({
    ...item,
    text: item.unitName,
    value: item.unitId,
  }))
}, [])

watch(
  () => formState.startTime,
  () => {
    const start = dayjs.duration(+formState.startTime[0], 'm').add(+formState.startTime[1], 's')
    const end = dayjs.duration(+formState.endTime[0], 'm').add(+formState.endTime[1], 's')

    if (end.asSeconds() <= start.asSeconds()) {
      formState.endTime = formState.startTime.slice()
    }
  },
  { flush: 'post' },
)

fetchInitData()

async function fetchInitData() {
  await stationsApi.load()
}

const onSubmit = useLoading(async () => {
  await formRef.value?.validate()

  const data: V1ManageTrainBookingsPostRequestBody = {
    userId: currentUserInfo.value.id!,
    unitId: formState.station,
    startTime: formatTime(formState.startTime),
    endTime: formatTime(formState.endTime),
  }

  await V1ManageTrainBookingsPost(data)

  showToast({
    type: 'success',
    message: '预约成功',
  })

  router.back()

  function formatTime(t: string[]) {
    return dayjs()
      .month(+formState.date[0] - 1)
      .date(+formState.date[1])
      .hour(+t[0])
      .minute(+t[1])
      .second(0)
      .format(`YYYY-MM-DD HH:mm:ss`) as any
  }
})
</script>

<template>
  <VanNavBar title="新增预约" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="pt-4">
    <VanForm ref="formRef" label-align="top">
      <PickerField v-model="formState.station" label="操作台" :columns="stationsApi.data.value" />
      <DatePickerField
        v-model="formState.date"
        label="日期"
        :columns-type="['month', 'day']"
        v-bind="datePickerConfig"
      />
      <TimePickerField
        v-model="formState.startTime"
        label="开始时间"
        :rules="[{ required: true, message: `请选择` }]"
      />
      <TimePickerField
        v-model="formState.endTime"
        label="结束时间"
        :rules="[{ required: true, message: `请选择` }]"
      />
    </VanForm>
  </div>
  <FixedButton @click="onSubmit" type="primary" :loading="onSubmit.isLoading"> 提交 </FixedButton>
</template>

<style lang="less" scoped></style>
