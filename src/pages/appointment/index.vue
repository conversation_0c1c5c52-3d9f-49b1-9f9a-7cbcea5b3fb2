<script lang="ts" setup>
import Footer from '@/components/layOut/footer.vue'
import { useLoading } from '@/hooks/useLoading'
import { useAsyncData } from '@/hooks/useAsyncData'
import { V1ManageSysUnitTerminalList, V1ManageTrainBookingsListPost } from '@/api/api.req'
import { StationStatus } from '@/enums/station'
import dayjs from 'dayjs'
import { getOption } from '@/utils'
import { AppointmentStatusOptions } from '@/enums/appointment'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { showToast } from 'vant'
import type { TimelineItem } from '@/components/WeeklyTimeline.vue'

const router = useRouter()

const query = useRouteQuery<{
  station?: string
  date?: string
}>()

const stationsList = useAsyncData(async () => {
  const resp = await V1ManageSysUnitTerminalList()

  const items = resp.map((item) => ({
    ...item,
    id: item.unitId!,
    label: item.unitName!,
    indicator: item.status === StationStatus.IN_USE,
  }))

  if (!items.find((i) => i.id === query.station)) {
    query.station = items.at(0)?.id
  }

  return items
}, [])

const appointmentList = useAsyncData(async () => {
  if (!query.date) {
    return []
  }
  if (!query.station) {
    return []
  }

  const selectedDay = dayjs(query.date, 'YYYY-MM-DD').startOf('d')

  const resp = await V1ManageTrainBookingsListPost({
    startTime: selectedDay.format('YYYY-MM-DD HH:mm:ss') as any,
    endTime: selectedDay.endOf('d').format('YYYY-MM-DD HH:mm:ss') as any,
    unitId: query.station,
  })

  const configs = resp.map((item) => ({
    ...item,
    start: dayjs(item.startTime).format('HH:mm'),
    end: dayjs(item.endTime).format('HH:mm'),
    title: item.userName!,
    color: getOption(AppointmentStatusOptions, item.status)?.color!,
  } satisfies TimelineItem))

  // sort by start time
  configs.sort((a, b) => {
    return dayjs(a.startTime).unix() - dayjs(b.startTime).unix()
  })

  return configs
}, [])

watch(
  () => [query.station, query.date],
  () => {
    appointmentList.load()
  },
)

const selectedStation = computed(() => stationsList.data.value.find((n) => n.id === query.station))

const fetchData = useLoading(_fetchData)
fetchData()

function _fetchData() {
  stationsList.load()
  appointmentList.load()
}

function onAddBtnClick() {
  if (!query.station) {
    showToast(`未查询到工位，请先配置工位！`)
    return
  }

  router.push({
    path: '/appointment/add',
    query: {
      station: query.station,
      date: query.date,
    },
  })
}
</script>

<template>
  <VanNavBar title="预约" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      disabled
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <div class="flex mb-2">
        <div class="flex-1 title">工作台状态</div>
        <div class="status">
          <StatusIndicator v-if="selectedStation?.indicator"> 使用中 </StatusIndicator>
        </div>
      </div>
      <StationsStatus :stations="stationsList.data.value" v-model="query.station" />
      <CardBox class="mt-4">
        <div class="title mb-4 flex gap-2">
          <span> 工作台预约 </span>
          <span class="flex-1 w-0 font-normal flex justify-end">
            {{ selectedStation?.label }}
          </span>
        </div>
        <div class="appointment">
          <WeeklyTimeline v-model="query.date" :timelines="appointmentList.data.value" />
        </div>
      </CardBox>
    </VanPullRefresh>

    <VanFloatingBubble
      class="floating-add-btn"
      icon="plus"
      axis="xy"
      :gap="{ x: 10, y: 60 }"
      @click="onAddBtnClick"
    />
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.title {
  font-weight: bold;
}
</style>

<style lang="less">
.van-floating-bubble.floating-add-btn {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background: var(--Brand-Brand7-Normal, #00996b);
  box-shadow: 4px 4px 4px 0px rgba(0, 0, 0, 0.12);

  .van-icon {
    font-size: 22px;
  }
}
</style>
