<script lang="ts" setup>
import TestPaperQuestionsRender from '@/pages/components/test-paper-questions-render.vue'
import {
  type TestPaperQuestionsGroupCell,
  splitGroups,
  testPaperQuestionsRenderOtherProps,
  verifyIsFillBlank,
  verifyIsMultip,
} from '@/pages/components/const'
import { QUESTION_TYPE, TEST_PAPER_MODE } from '@/config'
import {
  V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
  V1MobileHomeExamRecordPaperId,
} from '@/api/api.req'
import type {
  ExamRecordElement,
  PaperElement,
  QuestionBankColumnDTOElement,
  ResultAnswerList,
} from '@/api/api.model'
import Fields from '@/components/Fields.vue'

const router = useRouter()

const route = useRoute()

const testPaperQuestions = ref<TestPaperQuestionsGroupCell[]>([])

const testPaperInfo = ref<PaperElement>()

const examRecord = ref<ExamRecordElement>()

const loading = ref<boolean>(true)

const questionBankColumn = ref<QuestionBankColumnDTOElement[]>([])

/**
 * 加载所有题库字段配置
 */
async function loadAllQuestionBankColumn() {
  const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnListPost({})

  questionBankColumn.value = resp || []
}

function getUserAnswer(answerList: ResultAnswerList[], qId: string) {
  return answerList.find(item => item.questionId === qId) || {}
}

async function loadQuestions() {
  loading.value = true
  try {
    const id = route.query.id as string
    const resp = await V1MobileHomeExamRecordPaperId({ id })
    const qs = resp?.paper?.questionList || []
    const answerList = resp?.answerList || []

    const questionList = qs.map((item) => {
      const userAnswerInfo = getUserAnswer(answerList, item.id!)

      let answers = userAnswerInfo.answerList || []
      if (verifyIsMultip(item.questionType!)) {
        answers = answers.map((o) => {
          if (o === undefined || o === null || o === '') {
            return o
          }
          else {
            return Number(o)
          }
        }) as string[]
      }
      return {
        ...item,
        userAnswer:
          verifyIsFillBlank(item.questionType!)
          || item.questionType === QUESTION_TYPE.SHORT_ANSWER
          || verifyIsMultip(item.questionType!)
            ? answers
            : answers[0] === undefined
              ? undefined
              : Number(answers[0]),
        calcScore: userAnswerInfo.score || 0,
      }
    })
    testPaperQuestions.value = splitGroups(questionList)

    testPaperInfo.value = resp?.paper?.paper || {}
    examRecord.value = resp?.examRecord || {}
  }
  catch (error) {
    console.log(error)
  }
  finally {
    loading.value = false
  }
}
const testPaperInfoFields = computed(() => {
  const _propertyList = testPaperInfo.value?.questionPropertyList || []

  return _propertyList.map((o) => {
    const label
      = questionBankColumn.value.find(col => col.columnKey === o.columnKey)?.columnName || '-'
    return {
      label,
      value: o.columnValue,
    }
  })
})

onMounted(() => {
  loadQuestions()
  loadAllQuestionBankColumn()
})
</script>

<template>
  <VanNavBar :title="testPaperInfo?.name || '试卷名称'" />
  <div v-if="loading" class="h-200px flex items-center justify-center">
    <VanLoading type="spinner" />
  </div>
  <div v-else class="box-border h-100vh overflow-y-auto px-4 pt-4 pb-120px">
    <div class="relative min-h-[80px] overflow-hidden rounded-8px bg-[#fff]">
      <Fields :fields="testPaperInfoFields" />
      <div class="score-res-icon absolute right-20px top-10px">
        <div
          class="score-center absolute left-[-3px] top-17px h-26px w-66px flex items-center justify-center rounded-6px bg-[#fff] font-size-20px color-[#3ec05c]"
        >
          {{ examRecord?.score ?? '-' }}
          <span class="inline-block pl-2px pt-4px align-bottom font-size-14px">分</span>
        </div>
      </div>
    </div>
    <TestPaperQuestionsRender
      v-model:test-paper-value="testPaperQuestions"
      v-bind="testPaperQuestionsRenderOtherProps(TEST_PAPER_MODE.VIEW_USER_ANSWERS_AND_EXAMPLE)"
      :question-type-list="testPaperInfo?.questionTypeList || []"
    />
  </div>
  <div class="fixed-bottom">
    <VanButton type="primary" block @click="router.back()">
      返回
    </VanButton>
  </div>
</template>

<style lang="less" scoped>
.fixed-bottom {
  @apply fixed bottom-0 w-full border-1px border-color-[#f4f6f8] border-t-solid bg-[#fff] px-8px py-10px box-border;
}
:deep(.van-field__control:disabled) {
  color: #333 !important;
  -webkit-text-fill-color: #333;
}
:deep(.van-field__label) {
  color: #666 !important;
}
.score-res-icon {
  background-image: url(@/assets/icons/svg/score-res-icon.svg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 60px;
  height: 60px;
  .score-center {
    border: 1px solid #abd6c9;
  }
}
</style>

<style>
.van-floating-bubble.score-float {
  background-color: #6de8d0 !important;
}
</style>
