<script lang="ts" setup>
import Footer from '@/components/layOut/footer.vue'
import { useLoading } from '@/hooks/useLoading'
import { useDynamicList } from '@/hooks/useDynamicList'
import { currentUserInfo } from '@/store/sysUser'
import type {
  V1ManageExamRecordPagePostResponseResult,
  V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult,
  V1MobileHomePagePostRequestBodyData,
} from '@/api/api.model'
import { V1MobileHomeExamStatistic, V1MobileHomePagePost } from '@/api/api.req'
import { ExamStatusEnum, ExamStatusOptions, PaperTypeEnum } from '@/enums/exam'
import type { IconStatisticsProps } from '@/components/IconStatistics.vue'
import type { ListTabItem } from '@/components/CommonTabList.vue'
import { getOptionLabel } from '@/utils'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useRouteQuery } from '@/hooks/useRouteQuery'

enum TabId {
  PENDING = 'pending',
  EXAM_SKILL = 'examSkill',
  FINISHED = 'finished',
}

const router = useRouter()

const query = useRouteQuery<{
  tab?: TabId
}>()

const baseInfoApi = useAsyncData(V1MobileHomeExamStatistic, {})

const infos = computed(() => {
  const data = baseInfoApi.data.value || {}
  const configs: IconStatisticsProps[] = [
    {
      icon: 'svg/item/Profile',
      title: '待考试',
      value: data.waitingNum,
      unit: '课',
    },
    {
      icon: 'svg/item/Trophy',
      title: '技能考核',
      value: data.skillAssessmentNum,
      unit: '课',
    },
    {
      icon: 'svg/item/FileProtect',
      title: '已考试',
      value: data.finishNum,
      unit: '课',
    },
  ]

  return configs
})

const pendingExamListState = useDynamicList({
  api: async (params) => {
    const _params = {
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        type: PaperTypeEnum.SKILL_INSPECTION,
        statusList: [ExamStatusEnum.TO_BE],
      } as V1MobileHomePagePostRequestBodyData,
    }
    return convertToListItems(await V1MobileHomePagePost(_params))
  },
})

const examSkillListState = useDynamicList({
  api: async (params) => {
    const _params = {
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        type: PaperTypeEnum.SKILL_ASSESSMENT,
      } as V1MobileHomePagePostRequestBodyData,
    }

    return convertToListItems(await V1MobileHomePagePost(_params))
  },
})

const finishedExamListState = useDynamicList({
  api: async (params) => {
    const _params = {
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        type: PaperTypeEnum.SKILL_INSPECTION,
        statusList: [ExamStatusEnum.UNQUALIFIED, ExamStatusEnum.QUIT, ExamStatusEnum.COMPLETED],
      } as V1MobileHomePagePostRequestBodyData,
    }

    return convertToListItems(await V1MobileHomePagePost(_params))
  },
})

const tabsConfig = [
  { id: TabId.PENDING, name: '待考试', state: pendingExamListState },
  { id: TabId.EXAM_SKILL, name: '技能考核', state: examSkillListState },
  { id: TabId.FINISHED, name: '已考试', state: finishedExamListState },
]

const tabsState = reactive({
  activeTabId: query.tab ?? TabId.PENDING,
})

const currentListState = computed(() => {
  return tabsConfig.find((tab) => tab.id === tabsState.activeTabId)?.state || pendingExamListState
})

const fetchData = useLoading(_fetchData)

fetchData()

function convertToListItems(result: V1ManageExamRecordPagePostResponseResult) {
  return {
    ...result,
    records: result.records?.map((item) => {
      return {
        ...item,
        id: item.id!,
        name: item.paperName!,
        tag: getOptionLabel(ExamStatusOptions, item.status) || item.status!,
        linkText: item.status === ExamStatusEnum.TO_BE ? '去考试' : '去查看',
      }
    }),
  }
}

function _fetchData() {
  baseInfoApi.load()
  currentListState.value.reset()
  currentListState.value.load()
}

function gotoExamViewerPage(item: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult) {
  router.push({
    path: '/exam/view-answers',
    query: {
      id: item.id,
    },
  })
}

function gotoExamPage(item: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult) {
  router.push({
    path: '/exam/detail',
    query: {
      id: item.id,
    },
  })
}

function onClickListItem(item: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult) {
  if (item.paperType === PaperTypeEnum.SKILL_ASSESSMENT) {
    gotoExamViewerPage(item)
    return
  }

  if (item.status === ExamStatusEnum.TO_BE) {
    gotoExamPage(item)
    return
  }

  gotoExamViewerPage(item)
}

function onTabChanged(tabItem: ListTabItem) {
  query.tab = tabItem.id as TabId
  tabsState.activeTabId = tabItem.id as TabId
}
</script>

<template>
  <VanNavBar title="考试" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <CardBox class="flex justify-around mb-4">
        <IconStatistics v-for="info in infos" v-bind="info" />
      </CardBox>

      <CardBox class="pt-0!">
        <CommonTabList
          :tabs="tabsConfig"
          :active-tab-id="tabsState.activeTabId"
          @change="onTabChanged"
        />
        <CommonList :list-state="currentListState" @click-item="onClickListItem" />
      </CardBox>
    </VanPullRefresh>
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.list {
  @apply flex flex-col gap-2 mt-2;
}
</style>
