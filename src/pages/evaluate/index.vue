<script lang="ts">
export enum EvaluateTypeQuery {
  Reviewer = 'reviewer',
}
</script>

<script lang="ts" setup>
import type { PurpleAPIModel } from '@/api/api.model'
import { V1EvaluateTaskUserOrderPagePost } from '@/api/api.req'
import { EvaluateStatus } from '@/enums/evaluate'
import { useDynamicList } from '@/hooks/useDynamicList'
import { useLoading } from '@/hooks/useLoading'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { currentUserInfo } from '@/store/sysUser'
import EvaluateListItem from '@/components/EvaluateListItem.vue'

const router = useRouter()

const query = useRouteQuery<{
  type?: EvaluateTypeQuery
}>()

const isReviewer = query.type === EvaluateTypeQuery.Reviewer

const evaluateListState = useDynamicList({
  api: async (params) => {
    const resp = await V1EvaluateTaskUserOrderPagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        reviewer: isReviewer,
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.evaluateName!,
          linkText: isReviewer
            ? item.status?.toString() === EvaluateStatus.PENDING
              ? '去评审'
              : '去查看'
            : '去查看',
        }
      }),
    }
  },
})

const fetchData = useLoading(() => {
  evaluateListState.reset()
  evaluateListState.load()
})

fetchData()

function onClickBeTrainingItem(item: PurpleAPIModel) {
  router.push({
    path: '/evaluate/detail',
    query: {
      id: item.id,
      readonly: isReviewer ? (item.status?.toString() === EvaluateStatus.PENDING ? 0 : 1) : 1,
    },
  })
}
</script>

<template>
  <VanNavBar title="评定列表" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <CardBox>
        <CommonList :list-state="evaluateListState">
          <template #default="{ item }">
            <EvaluateListItem :item="item" @click="onClickBeTrainingItem(item)" />
          </template>
        </CommonList>
      </CardBox>
    </VanPullRefresh>
  </div>
</template>

<style lang="less" scoped></style>
