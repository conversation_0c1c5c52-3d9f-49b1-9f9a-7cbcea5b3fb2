<script lang="ts" setup>
export interface StationItem {
  id: string
  label: string
  indicator?: boolean
}

export interface StationStatusProps {
  modelValue?: string
  /**
   * @default 3
   */
  maxRow?: number
  stations: StationItem[]
}

const props = defineProps<StationStatusProps>()

const emit = defineEmits<{
  'update:modelValue': [value?: string]
  change: [station: StationItem]
}>()

const stationsStyle = computed(() => {
  const { maxRow = 3 } = props
  return {
    maxHeight: `${maxRow * 60 + 10 * (maxRow - 1)}px`,
  }
})

function onSelected(station: StationItem) {
  emit('update:modelValue', station.id)
  emit('change', station)
}
</script>

<template>
  <div class="stations" :style="stationsStyle">
    <div
      class="station-item"
      :class="{ 'is-selected': props.modelValue === station.id }"
      v-for="station in props.stations"
      :key="station.id"
      @click="onSelected(station)"
    >
      <div class="label" :title="station.label">
        {{ station.label }}
      </div>
      <div class="indicator" v-if="station.indicator"></div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.stations {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 10px;

  overflow: auto;
}

.station-item {
  height: 60px;
  @apply py-5 px-2 text-center;
  border-radius: 4px;
  background: white;
  position: relative;

  .label {
    @apply truncate;
    position: relative;
  }

  .indicator {
    position: absolute;
    @size: 5px;
    z-index: 1;

    width: @size;
    height: @size;
    border-radius: 99px;
    background: #2f7df3;
    margin-top: 4px;
    left: 50%;
    transform: translateX(-50%);
  }

  &.is-selected {
    background: #00996b;
    color: white;

    .indicator {
      background: #005ce6;
      box-sizing: content-box;
      border: 1px solid rgba(255, 255, 255, 0.9);
    }
  }
}
</style>
