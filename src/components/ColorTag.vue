<script lang="ts">
export enum ColorTagColor {
  Yellow = 'yellow',
  Green = 'green',
  Gray = 'gray',
}

export type ColorTagColorType = ColorTagColor | `${ColorTagColor}`
</script>

<script lang="ts" setup>
export interface ColorTagProps {
  shape?: 'round'
  border?: boolean
  color?: ColorTagColorType
}

defineProps<ColorTagProps>()
</script>

<template>
  <div class="color-tag" :class="[color, shape, { border }]">
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.color-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 8px;
  display: inline-block;

  color: #fc8800;
  background: rgba(252, 136, 0, 0.24);

  &.border {
    border: 1px solid transparent;
  }

  &.yellow {
    color: #fc8800;
    background: rgba(252, 136, 0, 0.24);
  }

  &.green {
    color: #00996b;
    background: #ebfaec;
    border-color: #00996b;
  }

  &.gray {
    color: #5e5e5e;
    background: #eee;
  }

  &.round {
    border-radius: 20px;
    @apply px-10px;
  }
}
</style>
