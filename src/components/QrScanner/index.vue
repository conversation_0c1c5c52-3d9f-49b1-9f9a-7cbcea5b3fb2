<script setup lang="ts">
import type { ScanResult } from '@/utils/wechat'
import { showFailToast, showSuccessToast } from 'vant'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { axiosIns } from '@/api/axios'
import { initWechatSDK } from '@/components/QrScanner/wechat'
import { isWechatBrowser, wechatSDK } from '@/utils/wechat'

const props = withDefaults(defineProps<Props>(), {
  buttonText: '扫一扫',
  buttonType: 'primary',
  buttonSize: 'normal',
  buttonClass: '',
  disabled: false,
})

const router = useRouter()

interface Props {
  buttonText?: string
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  buttonSize?: 'large' | 'normal' | 'small' | 'mini'
  buttonClass?: string
  disabled?: boolean
  onSuccess?: (result: string, parsedResult?: any) => void
  onError?: (error: Error) => void
}

const scanning = ref(false)
const scanResult = ref('')
const parsedResult = ref<any>(null)
const showResult = ref(false)
const showLoginDialog = ref(false)
const loginData = ref<any>(null)
const loginLoading = ref(false)
const wxInitialized = ref(false)
const wxInitError = ref('')

// 检测是否在微信环境中
const isInWechat = computed(() => isWechatBrowser())

// 检测是否支持微信扫码API
const supportWxScan = computed(() => {
  return isInWechat.value && wxInitialized.value && wechatSDK.getStatus() === 'ready'
})

// 初始化微信SDK
async function initWechat() {
  if (!isInWechat.value) {
    console.log('不在微信环境中，跳过微信SDK初始化')
    return
  }

  try {
    console.log('开始初始化微信SDK...')

    // 获取微信配置
    const wxConfig = await initWechatSDK(false) // 生产环境设为false

    // 配置微信SDK
    await wechatSDK.configWx(wxConfig)

    wxInitialized.value = true
    wxInitError.value = ''
    console.log('微信SDK初始化成功')
  }
  catch (error) {
    console.error('微信SDK初始化失败:', error)
    wxInitError.value = String(error)
    wxInitialized.value = false
  }
}

// 处理扫码
async function handleScan() {
  if (scanning.value || props.disabled)
    return

  try {
    scanning.value = true

    // 优先使用微信扫码API
    if (supportWxScan.value) {
      await scanWithWechat()
    }
    else {
      // 暂时使用模拟扫码，后续可以集成摄像头扫码
      await simulateScan()
    }
  }
  catch (error) {
    console.error('扫码失败:', error)
    props.onError?.(error as Error)
  }
  finally {
    scanning.value = false
  }
}

// 微信扫码
async function scanWithWechat() {
  try {
    console.log('开始微信扫码...')

    const result = await wechatSDK.scanQRCode({
      needResult: 1,
      scanType: ['qrCode', 'barCode'],
      success: (res: ScanResult) => {
        console.log('微信扫码成功:', res)
      },
      fail: (error: any) => {
        console.error('微信扫码失败:', error)
      },
    })

    console.log('扫码结果:', result)
    handleScanResult(result.resultStr)
    return result.resultStr
  }
  catch (error) {
    console.error('微信扫码异常:', error)
    throw new Error(`微信扫码失败: ${error}`)
  }
}

// 模拟扫码（用于开发测试）
async function simulateScan() {
  await new Promise(resolve => setTimeout(resolve, 1000))

  const mockResults = [
    {
      type: 'workstation_info',
      workstationId: '1868544392901300226',
      workstationName: '工位03',
      levelName: '工厂_车间1',
      userId: '1866000231912005633',
      userName: 'panda',
      projectId: '1904413530179993601',
      projectName: '密封胶条01',
      countAlgorithm: 'sealingRubber',
      videoUrl: '/example.mp4',
      source: 'cddc_training_system',
      timestamp: Date.now(),
    },
    {
      type: 'workstation_info',
      workstationId: '1868544392901300226',
      workstationName: '工位03',
      levelName: '工厂_车间1',
      userId: '1873555920079618050',
      userName: '道场测试号',
      projectId: '1931916000400097282',
      projectName: '线束安装',
      countAlgorithm: 'harnessConnector',
      videoUrl: 'http://*************:9000/gaea/PRODUCT/20250619/d18730da6c91434babe9ee70b371c624.mp4',
      source: 'cddc_training_system',
      version: '1.0',
      timestamp: Date.now(),
    },
    {
      type: 'login',
      token: '07B9D009-CAFA-4F01-A4A9-9D5DB2A5D20F',
      deviceIp: '************',
      timestamp: Date.now(),
    },
  ]

  const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)]
  handleScanResult(JSON.stringify(randomResult))
}

// 处理扫码结果
function handleScanResult(result: string) {
  scanResult.value = result
  parsedResult.value = parseQrCodeData(result)

  // 根据扫码结果类型进行不同处理
  const resultType = parsedResult.value?.type

  switch (resultType) {
    case 'workstation_info':
      handleWorkstationInfo(parsedResult.value)
      break
    case 'login':
      handleLoginConfirm(parsedResult.value)
      break
    default:
      showResult.value = true
      break
  }

  props.onSuccess?.(result, parsedResult.value)
}

// 解析二维码数据
function parseQrCodeData(data: string) {
  try {
    // 尝试解析JSON格式的二维码
    const parsed = JSON.parse(data)
    return parsed
  }
  catch {
    // 如果不是JSON格式，检查是否是URL
    if (data.startsWith('http://') || data.startsWith('https://')) {
      return {
        type: 'url',
        url: data,
      }
    }
    // 其他格式的数据
    return {
      type: 'text',
      content: data,
    }
  }
}

// 处理工位信息
function handleWorkstationInfo(data: any) {
  // 跳转到工位信息页面，传递真实的工位数据
  const queryData = encodeURIComponent(JSON.stringify(data))
  router.push({
    path: '/workstation/info',
    query: {
      data: queryData,
      workstationId: data.workstationId,
      projectId: data.projectId,
    },
  })
}

// 处理登录确认
function handleLoginConfirm(data: any) {
  showLoginDialog.value = true
  loginData.value = data
}

// 调用登录确认API
async function confirmLoginApi(token: string) {
  const formData = new FormData()
  formData.append('token', token)

  return axiosIns.post('/v1/location/home/<USER>/confirm-login', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 处理登录确认
async function handleLoginConfirmAction() {
  if (loginLoading.value)
    return

  try {
    loginLoading.value = true
    console.log('确认登录:', loginData.value)

    if (!loginData.value?.token) {
      showFailToast('缺少登录token')
      return
    }

    // 调用登录确认API
    const response = await confirmLoginApi(loginData.value.token)
    console.log('登录成功:', response)

    showSuccessToast('登录成功')
    closeLoginDialog()
  }
  catch (error) {
    console.error('登录失败:', error)
    showFailToast('登录失败，请重试')
  }
  finally {
    loginLoading.value = false
  }
}

// 关闭登录弹窗
function closeLoginDialog() {
  showLoginDialog.value = false
  loginData.value = null
}

// 关闭结果弹窗
function handleResultConfirm() {
  showResult.value = false
  scanResult.value = ''
  parsedResult.value = null
}

// 组件挂载时初始化
onMounted(() => {
  // 在微信环境中初始化微信SDK
  if (isInWechat.value) {
    initWechat()
  }
})

// 组件销毁时清理资源
onUnmounted(() => {
  showResult.value = false
  showLoginDialog.value = false
  scanning.value = false
  loginLoading.value = false
})
</script>

<template>
  <div class="qr-scanner">
    <!-- 扫码按钮 -->
    <van-button
      :type="buttonType"
      :size="buttonSize"
      :loading="scanning"
      :disabled="disabled || (isInWechat && !wxInitialized)"
      :class="buttonClass"
      @click="handleScan"
    >
      <template v-if="buttonText" #icon>
        <SvgIcon name="svg/scan" size="20" />
        <!-- <van-icon name="scan" size="24" /> -->
      </template>
      <van-icon v-if="!buttonText" name="scan" size="24" />
      {{ buttonText }}
      <!-- 微信环境下显示初始化状态 -->
      <span v-if="isInWechat && !wxInitialized && !scanning" class="ml-1 text-xs text-gray-5">
        (初始化中...)
      </span>
    </van-button>

    <!-- 扫码结果弹窗 -->
    <van-dialog
      v-model:show="showResult"
      title="扫码结果"
      :show-cancel-button="false"
      confirm-button-text="确定"
      @confirm="handleResultConfirm"
    >
      <div class="result-content p-4">
        <div v-if="scanResult" class="result-info">
          <div class="result-item mb-3">
            <div class="label mb-1 text-sm text-gray-6">
              扫码内容：
            </div>
            <div class="value break-all text-sm">
              {{ scanResult }}
            </div>
          </div>
          <div v-if="parsedResult" class="parsed-result">
            <div class="label mb-1 text-sm text-gray-6">
              解析结果：
            </div>
            <div class="parsed-content text-sm">
              <div v-if="parsedResult.type" class="mb-1">
                <span class="font-medium">类型：</span>{{ parsedResult.type }}
              </div>

              <!-- 工位信息显示 -->
              <template v-if="parsedResult.type === 'workstation_info'">
                <div v-if="parsedResult.workstationName" class="mb-1">
                  <span class="font-medium">工位名称：</span>{{ parsedResult.workstationName }}
                </div>
                <div v-if="parsedResult.levelName" class="mb-1">
                  <span class="font-medium">所属层级：</span>{{ parsedResult.levelName }}
                </div>
                <div v-if="parsedResult.userName" class="mb-1">
                  <span class="font-medium">操作员：</span>{{ parsedResult.userName }}
                </div>
                <div v-if="parsedResult.projectName" class="mb-1">
                  <span class="font-medium">项目名称：</span>{{ parsedResult.projectName }}
                </div>
                <div v-if="parsedResult.countAlgorithm" class="mb-1">
                  <span class="font-medium">计数算法：</span>{{ parsedResult.countAlgorithm }}
                </div>
                <div v-if="parsedResult.source" class="mb-1">
                  <span class="font-medium">数据源：</span>{{ parsedResult.source }}
                </div>
              </template>

              <!-- 登录信息显示 -->
              <template v-if="parsedResult.type === 'login'">
                <div v-if="parsedResult.deviceIp" class="mb-1">
                  <span class="font-medium">设备IP：</span>{{ parsedResult.deviceIp }}
                </div>
                <div v-if="parsedResult.token" class="mb-1">
                  <span class="font-medium">Token：</span>{{ parsedResult.token.substring(0, 8) }}...
                </div>
              </template>
            </div>
          </div>
        </div>
        <div v-else class="no-result py-4 text-center text-gray-5">
          未识别到有效二维码
        </div>
      </div>
    </van-dialog>

    <!-- 登录确认弹窗 -->
    <van-dialog
      v-model:show="showLoginDialog"
      show-cancel-button
      confirm-button-text="确认登录"
      cancel-button-text="取消"
      :confirm-button-loading="loginLoading"
      :cancel-button-disabled="loginLoading"
      @confirm="handleLoginConfirmAction"
      @cancel="closeLoginDialog"
    >
      <div class="login-content p-4">
        <div v-if="loginData" class="login-info">
          <div class="mb-4 text-center">
            <div class="text-lg text-[#333] font-bold">
              确认登录到以下设备？
            </div>
          </div>

          <div class="device-info rounded-lg bg-[#EBFAEC] p-3">
            <div v-if="loginData.deviceIp" class="info-item">
              <span class="label">设备IP：</span>
              <span class="value">{{ loginData.deviceIp }}</span>
            </div>
            <div class="info-item">
              <span class="label">登录时间：</span>
              <span class="value">{{ new Date().toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style lang="less" scoped>
.qr-scanner {
  display: inline-block;
}

.result-content {
  .result-info {
    .result-item {
      .label {
        font-weight: 500;
      }

      .value {
        color: #333;
        line-height: 1.4;
      }
    }

    .parsed-result {
      .parsed-content {
        background: #f7f8fa;
        border-radius: 4px;
        padding: 8px;

        > div {
          color: #666;

          .font-medium {
            color: #333;
          }
        }
      }
    }
  }
}

.login-content {
  .device-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: #666;
      }

      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
