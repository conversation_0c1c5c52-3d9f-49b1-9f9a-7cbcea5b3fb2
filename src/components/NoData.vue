<script lang="ts" setup>
interface NoDataProps {
  /**
   * 提示文字
   * @default '暂无数据'
   */
  tip?: string
  /**
   * 尺寸大小
   * @default 'normal'
   */
  size?: 'small' | 'normal' | 'large'
}

const props = withDefaults(defineProps<NoDataProps>(), {
  tip: '暂无数据',
  size: 'normal',
})

// 根据尺寸计算样式
const sizeConfig = computed(() => {
  switch (props.size) {
    case 'small':
      return {
        imageSize: 'h-60px w-60px',
        padding: 'py-16px',
        textSize: 'text-xs',
      }
    case 'large':
      return {
        imageSize: 'h-100px w-100px',
        padding: 'py-24px',
        textSize: 'text-base',
      }
    default:
      return {
        imageSize: 'h-80px w-80px',
        padding: 'py-20px',
        textSize: 'text-sm',
      }
  }
})
</script>

<template>
  <div :class="sizeConfig.padding">
    <div class="flex justify-center">
      <div :class="sizeConfig.imageSize">
        <img class="h-full w-full" src="@/assets/empty.svg" alt="暂无数据">
      </div>
    </div>
    <div 
      :class="[
        'flex justify-center pt-20px color-[#999]',
        sizeConfig.textSize
      ]"
    >
      {{ tip }}
    </div>
  </div>
</template>

<style lang="less" scoped>
</style>
