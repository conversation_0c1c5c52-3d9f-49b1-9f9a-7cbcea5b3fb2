<script lang="ts" setup>
import type { ButtonProps } from 'vant'

export interface FixedButtonProps extends Partial<ButtonProps> {}

const props = defineProps<FixedButtonProps>()
</script>

<template>
  <div class="fixed-button">
    <VanButton v-bind="props" block>
      <slot></slot>
    </VanButton>
  </div>
</template>

<style lang="less" scoped>
.fixed-button {
  @apply fixed bottom-0 left-0 w-full border-1px border-color-[#f4f6f8] border-t-solid bg-[#fff] px-8px py-10px box-border;
}
</style>
