<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { FieldRule, FieldTextAlign } from 'vant'

export interface PopupFieldProps {
  label?: string
  placeholder?: string

  visible?: boolean
  lazyRender?: boolean

  disabled?: boolean

  content?: string

  rules?: FieldRule[]
  labelAlign?: FieldTextAlign
}

const props = defineProps<PopupFieldProps>()

const emit = defineEmits(['update:visible'])

const vShow = useVModel(props, 'visible', emit, {
  passive: true,
})

function open() {
  if (props.disabled) return

  vShow.value = true
}
</script>

<template>
  <VanField
    is-link
    readonly
    :label="label"
    :model-value="content"
    :placeholder="placeholder || `请选择`"
    :disabled="disabled"
    :rules="rules"
    :label-align="labelAlign"
    @click="open()"
  />
  <VanPopup
    v-model:show="vShow"
    round
    position="bottom"
    :lazyRender="lazyRender"
    :z-index="999999"
  >
    <slot> </slot>
  </VanPopup>
</template>

<style lang="less" scoped></style>
