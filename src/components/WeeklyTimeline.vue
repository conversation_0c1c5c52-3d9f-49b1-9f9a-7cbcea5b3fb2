<script lang="ts" setup>
import dayjs, { Dayjs } from 'dayjs'

interface DayItem {
  instance: Dayjs
  datetime: string
  date: number
  day: number
  dayName: string
}

export interface TimelineItem {
  start: string
  end: string
  title: string
  desc?: string
  color: string
}

export interface WeeklyTimelineProps {
  modelValue?: string
  timelines?: TimelineItem[]
  loading?: boolean
}

const props = defineProps<WeeklyTimelineProps>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const days = getWeekDays()

function getWeekDays() {
  const startDay = dayjs().startOf('day')
  const days: DayItem[] = []

  for (let i = 0; i < 7; i++) {
    const dayInstance = startDay.add(i, 'day')

    days.push({
      instance: dayInstance,
      datetime: dayInstance.format('YYYY-MM-DD'),
      date: dayInstance.date(),
      day: dayInstance.day(),
      dayName: dayInstance.format('ddd'),
    })
  }

  if (!days.find((n) => n.datetime === props.modelValue)) {
    emit('update:modelValue', days.at(0)!.datetime)
  }

  return days
}

function selectDate(datetime: string) {
  emit('update:modelValue', datetime)
}
</script>

<template>
  <div class="weekly-timeline">
    <div class="days">
      <div
        v-for="day in days"
        :key="day.date"
        class="day"
        :class="{ 'is-selected': props.modelValue === day.datetime }"
        @click="selectDate(day.datetime)"
      >
        <span class="day-date">{{ day.date }}</span>
        <span class="day-name">{{ day.dayName }}</span>
      </div>
    </div>

    <div class="timelines" v-if="timelines?.length">
      <template v-for="item in timelines" :key="item.start">
        <div class="timeline-item start">
          <div class="time">
            {{ item.start }}
          </div>
          <div class="color-block" :style="{ background: item.color }"></div>
          <div class="flex flex-col justify-center">
            <div class="label">
              {{ item.title }}
            </div>
            <div class="user">
              {{ item.desc }}
            </div>
          </div>
        </div>
        <div class="timeline-item end">
          <div class="time">
            {{ item.end }}
          </div>
          <div class="color-block" :style="{ background: item.color }"></div>
        </div>
      </template>
    </div>
    <div v-else>
      <EmptyData></EmptyData>
    </div>
  </div>
</template>

<style lang="less" scoped>
.weekly-timeline {
  .days {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: stretch;
    width: 100%;
  }

  .day {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60px;
    background: #ffffff;
    border-radius: 4px;
    padding: 4px 0 6px;
    cursor: pointer;
    gap: 2px;
    min-width: 0;
    transition: all 0.2s ease;

    .day-date {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
    }

    .day-name {
      font-family: 'PingFang SC', sans-serif;
      font-size: 10px;
      line-height: 16px;
      color: rgba(0, 0, 0, 0.4);
    }

    &.is-selected {
      background: #00996b;

      .day-date,
      .day-name {
        color: #ffffff;
      }
    }
  }
}

.timelines {
  background: white;

  margin-top: 16px;
  font-size: 14px;

  .timeline-item {
    display: flex;

    height: 50px;

    .time {
      width: 50px;
      border-right: 1px solid #dddddd;
      display: flex;
      align-items: center;
      margin-right: 8px;
      color: #666;
    }

    .color-block {
      width: 32px;

      border-radius: 8px 8px 0 0;
      margin-top: 8px;
      margin-right: 16px;
    }

    .label {
      color: var(--text-icon-font-1, #333);
      font-size: 14px;
      margin-bottom: 8px;
    }

    .user {
      color: var(--text-icon-font-3, #999);
      font-size: 12px;
    }

    &.end {
      .color-block {
        border-radius: 0 0 8px 8px;
        margin-top: 0;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
