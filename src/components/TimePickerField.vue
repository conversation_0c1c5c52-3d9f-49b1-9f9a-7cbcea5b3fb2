<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { TimePickerColumnType } from 'vant'
import type { PopupFieldProps } from './PopupFieldItem.vue'
import dayjs from 'dayjs'

export interface TimeFieldProps extends PopupFieldProps {
  modelValue?: string[]
  minTime?: Date
  maxTime?: Date
  columnsType?: TimePickerColumnType[]
}

const props = defineProps<TimeFieldProps>()

const vModel = useVModel(props, 'modelValue')

const emit = defineEmits(['update:modelValue'])

const state = reactive({
  visible: false,
})

const content = computed(() => vModel.value?.join(':'))

function formatTime(date?: Date) {
  if (!date) return

  return dayjs(date).format('HH:mm:ss')
}

function onConfirm() {
  state.visible = false
}
function onCancel() {
  state.visible = false
}
</script>

<template>
  <PopupFieldItem
    :label="label"
    :placeholder="placeholder"
    lazy-render
    :disabled="disabled"
    :content="content"
    :rules="rules"
    v-model:visible="state.visible"
  >
    <VanTimePicker
      v-model="vModel"
      :min-time="formatTime(minTime)"
      :max-time="formatTime(maxTime)"
      :columns-type="columnsType"
      @confirm="onConfirm"
      @cancel="onCancel"
    />
  </PopupFieldItem>
</template>

<style lang="less" scoped></style>
