<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { DatePickerColumnType } from 'vant'
import type { PopupFieldProps } from './PopupFieldItem.vue'

export interface DateFieldProps extends PopupFieldProps {
  modelValue?: string[]
  minDate?: Date
  maxDate?: Date
  columnsType?: DatePickerColumnType[]
}

const props = defineProps<DateFieldProps>()

const vModel = useVModel(props, 'modelValue')

const emit = defineEmits(['update:modelValue'])

const state = reactive({
  visible: false,
})

const content = computed(() => vModel.value?.join('-'))

function onConfirm() {
  state.visible = false
}
function onCancel() {
  state.visible = false
}
</script>

<template>
  <PopupFieldItem
    :label="label"
    :placeholder="placeholder"
    :lazy-render="lazyRender"
    :disabled="disabled"
    :content="content"
    :rules="rules"
    v-model:visible="state.visible"
  >
    <VanDatePicker
      v-model="vModel"
      :min-date="minDate"
      :max-date="maxDate"
      :columns-type="columnsType"
      @confirm="onConfirm()"
      @cancel="onCancel()"
    />
  </PopupFieldItem>
</template>

<style lang="less" scoped></style>
