import { V1LocationHomeAlarmDetailsStatisticsPost, V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId } from '@/api/api.req'
import defBgImage from '@/assets/img/def-bg.png'
import iconStandard from '@/assets/img/icon-standard.png'
import iconUnqualified from '@/assets/img/icon-unqualified.png'
import { ACTION_TYPES_WRONG } from '@/config/table-columns'

// 训练记录数据接口
export interface TrainingRecord {
  date: string
  workPassRate: string
  workCount: string
  workFailCount: string
  actionRate: string
  actionFailCount: string
  images?: {
    imageUrl: string
    description: string
    iconImg: string
    time: string
    recordDetailAlarmId?: string // 图片ID，用于懒加载
    loaded?: boolean // 是否已加载
  }[]
}

/**
 * 通用fetchTabData方法，用于获取训练/考核/比赛等报告的tab数据
 * @param {string} tabId - 当前tab的id
 * @param {Function} setRecords - 设置训练记录的回调
 * @param {object} [options] - 额外配置项
 * @returns {Promise<void>}
 */
export async function fetchTabDataCommon(
  tabId: string,
  setRecords: (records: any[]) => void,
  options: { actionTypes?: any[], result?: number, standard?: number } = {},
) {
  try {
    const response = await V1LocationHomeAlarmDetailsStatisticsPost({
      actionTypes: options.actionTypes || [],
      recordId: tabId,
      result: options.result ?? 0,
      standard: options.standard ?? 0,
    })
    if (response && Array.isArray(response)) {
      const allRecords = await Promise.all(
        response.map(async (record: any) => {
          let images: any[] = []
          if (
            record.alarmItems
            && Array.isArray(record.alarmItems)
            && record.alarmItems.length > 0
          ) {
            const initialLoadCount = 2
            images = await Promise.all(
              record.alarmItems.map(async (item: any, index: number) => {
                let imageUrl = defBgImage
                let loaded = false
                if (item.recordDetailAlarmId && index < initialLoadCount) {
                  try {
                    const imageResponse
                      = await V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId(
                        {
                          recordDetailAlarmId: item.recordDetailAlarmId,
                        },
                      )
                    imageUrl = imageResponse || imageUrl
                    loaded = true
                  }
                  catch (error) {}
                }
                let description = '未知错误'
                if (item.actionType !== undefined && item.actionType !== null) {
                  const match = ACTION_TYPES_WRONG.find(
                    e => e.value === item.actionType,
                  )
                  if (match) {
                    description = `${match.label}`
                  }
                }
                return {
                  imageUrl,
                  description,
                  iconImg:
                    item.result == 0
                      ? iconUnqualified
                      : iconStandard,
                  time: item.timestamp || '',
                  recordDetailAlarmId: item.recordDetailAlarmId,
                  loaded,
                }
              }),
            )
          }
          return {
            date: record.createdDate || '',
            workPassRate:
              Math.floor((record.opNumOk / record.opNum) * 100) || '0',
            workCount: record.opNum || 0,
            workFailCount: record.opNum - record.opNumOk || '0',
            actionRate:
              Math.floor((record.actionNumOk / record.actionNum) * 100) || '0',
            actionFailCount: record.actionNum - record.actionNumOk,
            images: images.length > 0 ? images : undefined,
          }
        }),
      )
      setRecords(allRecords)
    }
    else {
      setRecords([])
    }
  }
  catch (error) {
    setRecords([])
    // 可以根据需要抛出或处理错误
    // console.error('获取数据失败:', error)
  }
}

/**
 * 格式化百分比数值
 * @param value 数值
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number | string): string {
  const num = typeof value === 'string' ? Number.parseFloat(value) : value
  return Number.isNaN(num) ? '0%' : `${Math.round(num)}%`
}

/**
 * 计算合格率
 * @param passed 合格数量
 * @param total 总数量
 * @returns 合格率百分比
 */
export function calculatePassRate(passed: number, total: number): number {
  if (total === 0)
    return 0
  return Math.floor((passed / total) * 100)
}
