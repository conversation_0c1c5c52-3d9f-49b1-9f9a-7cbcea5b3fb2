export enum EvaluateStatus {
  /**
   * 待评审
   */
  PENDING = '0',

  /**
   *
   * 合格
   */
  QUALIFIED = '1',

  /**
   * 不合格
   */
  UNQUALIFIED = '2',
}

export const EvaluateStatusOptions = [
  {
    label: '待评审',
    value: EvaluateStatus.PENDING,
  },
  {
    label: '合格',
    value: EvaluateStatus.QUALIFIED,
  },
  {
    label: '不合格',
    value: EvaluateStatus.UNQUALIFIED,
  },
]

export enum EvaluateResult {
  /**
   * 合格
   */
  QUALIFIED = '1',
  /**
   * 不合格
   */
  UNQUALIFIED = '0',
}

export const EvaluateResultOptions = [
  {
    label: '合格',
    value: EvaluateResult.QUALIFIED,
  },
  {
    label: '不合格',
    value: EvaluateResult.UNQUALIFIED,
  },
]