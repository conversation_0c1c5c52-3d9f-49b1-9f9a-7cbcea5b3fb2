import type { Dayjs, UnitType } from 'dayjs'
import dayjs from 'dayjs'

export function sleep(ms = 0) {
  return new Promise<void>(resolve => setTimeout(resolve, ms))
}

export interface FormatPickerRangeOptions {
  /**
   * @default 'day'
   */
  unit?: UnitType
  /**
   * @default 'YYYY-MM-DD HH:mm:ss'
   */
  format?: string
  /**
   * @default ['start', 'end']
   */
  nameMapper?: string[]
}

export function formatPickerRange(
  input: [Dayjs, Dayjs] | undefined,
  opt: FormatPickerRangeOptions = {},
) {
  if (!input || input.length < 2)
    return {}

  const {
    //
    unit = 'day',
    format = 'YYYY-MM-DD HH:mm:ss',
    nameMapper = ['start', 'end'],
  } = opt

  const [start, end] = input

  const obj = {
    [nameMapper[0]]: start.startOf(unit).format(format),
    [nameMapper[1]]: end.endOf(unit).format(format),
  }

  return obj
}

export function formatDate(date?: number | Dayjs, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date)
    return

  return dayjs(`${date}`.length < 13 ? +date * 1000 : date).format(format)
}

export function formatDateRange(start?: number, end?: number) {
  if (!start && !end)
    return

  return [formatDate(start) || '-', formatDate(end) || '-'].join(' ~ ')
}

/**
 *
 * @example
 * ```ts
 * getWeekName(0) // 周一
 * getWeekName(6) // 周日
 * ```
 * @param weekDay
 */
export function getWeekName(weekDay: number) {
  const names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

  return names[weekDay]
}

export function parseDate(
  date: string | number | null | undefined | Dayjs,
  format = 'YYYY-MM-DD HH:mm:ss',
) {
  if (typeof date === 'number') {
    return dayjs(date)
  }

  return dayjs(date).format(format)
}

export function secondsToHMS(seconds: number = 0) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  return `${hours > 0 ? `${hours}小时` : ''}${(hours > 0 || minutes > 0) ? `${minutes}分钟` : ''}${remainingSeconds}秒`
}

/**
 * 格式化时长（秒）为可读格式
 * @param seconds 秒数
 * @returns 格式化后的时长字符串，如 "1小时30分钟" 或 "30分钟" 或 "45秒"
 */
export function formatDuration(seconds: number = 0): string {
  if (seconds <= 0)
    return '0s'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  const parts: string[] = []

  if (hours > 0) {
    parts.push(`${hours}h`)
  }

  if (minutes > 0) {
    parts.push(`${minutes}min`)
  }

  if (remainingSeconds > 0 || parts.length === 0) {
    parts.push(`${remainingSeconds}s`)
  }

  return parts.join('')
}
