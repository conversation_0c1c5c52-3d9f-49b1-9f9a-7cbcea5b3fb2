import { showImagePreview } from 'vant'
import { router } from '@/router'

// 图片预览项接口
export interface ImagePreviewItem {
  url: string
  description?: string
  time?: string
  iconImg?: string
}

/**
 * 使用Vant的ImagePreview组件预览图片
 * @param images 图片数组
 * @param startIndex 起始索引
 */
export function previewImagesWithVant(images: string[], startIndex = 0) {
  showImagePreview({
    images,
    startPosition: startIndex,
    closeable: true,
    showIndex: true,
    showIndicators: true,
    swipeDuration: 300,
  })
}

/**
 * 使用自定义页面预览图片（支持更多功能）
 * @param images 图片数据数组
 * @param startIndex 起始索引
 */
export function previewImagesWithCustomPage(images: ImagePreviewItem[], startIndex = 0) {
  try {
    // 将图片数据保存到sessionStorage
    sessionStorage.setItem('previewImageData', JSON.stringify(images))
    sessionStorage.setItem('previewCurrentIndex', startIndex.toString())

    // 跳转到图片预览页面
    router.push('/image-preview')
  }
  catch (error) {
    console.error('预览图片失败:', error)

    // 如果自定义预览失败，回退到Vant的ImagePreview
    const imageUrls = images.map(item => item.url)
    previewImagesWithVant(imageUrls, startIndex)
  }
}

/**
 * 智能图片预览 - 根据图片数据自动选择预览方式
 * @param images 图片数据
 * @param startIndex 起始索引
 */
export function previewImages(images: ImagePreviewItem[] | string[], startIndex = 0) {
  // 如果是字符串数组，使用Vant的ImagePreview
  if (typeof images[0] === 'string') {
    previewImagesWithVant(images as string[], startIndex)
    return
  }

  // 如果是对象数组，检查是否有额外信息
  const imageItems = images as ImagePreviewItem[]
  const hasExtraInfo = imageItems.some(item =>
    item.description || item.time || item.iconImg !== undefined,
  )

  if (hasExtraInfo) {
    // 有额外信息，使用自定义页面
    previewImagesWithCustomPage(imageItems, startIndex)
  }
  else {
    // 没有额外信息，使用Vant的ImagePreview
    const imageUrls = imageItems.map(item => item.url)
    previewImagesWithVant(imageUrls, startIndex)
  }
}

/**
 * 从图片URL创建ImagePreviewItem
 * @param urls 图片URL数组
 * @returns ImagePreviewItem数组
 */
export function createImagePreviewItems(urls: string[]): ImagePreviewItem[] {
  return urls.map(url => ({ url }))
}

/**
 * 检查是否支持自定义图片预览页面
 * @returns 是否支持
 */
export function isCustomPreviewSupported(): boolean {
  try {
    // 检查sessionStorage是否可用
    sessionStorage.setItem('test', 'test')
    sessionStorage.removeItem('test')
    return true
  }
  catch {
    return false
  }
}
